const { validatePhoneNumber } = require('./phoneValidator');

// Test cases for phone number validation
const testCases = [
    // Valid US numbers
    { phone: '+15551234567', country: 'US', description: 'Valid US number with country code' },
    { phone: '5551234567', country: 'US', description: 'Valid US number without country code' },
    { phone: '(*************', country: 'US', description: 'Valid US number with formatting' },
    
    // Valid international numbers
    { phone: '+************', country: 'GB', description: 'Valid UK mobile number' },
    { phone: '+33123456789', country: 'FR', description: 'Valid French number' },
    { phone: '+************', country: 'IN', description: 'Valid Indian number' },
    
    // Invalid numbers
    { phone: '123', country: 'US', description: 'Too short number' },
    { phone: '12345678901234567890', country: 'US', description: 'Too long number' },
    { phone: 'abcdefghij', country: 'US', description: 'Non-numeric characters' },
    { phone: '', country: 'US', description: 'Empty phone number' },
    
    // Edge cases
    { phone: '0000000000', country: 'US', description: 'All zeros' },
    { phone: '1111111111', country: 'US', description: 'All ones' },
];

async function runTests() {
    console.log('🧪 Starting Phone Number Validation Tests\n');
    console.log('=' .repeat(60));
    
    let passedTests = 0;
    let totalTests = testCases.length;
    
    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`\nTest ${i + 1}: ${testCase.description}`);
        console.log(`Phone: "${testCase.phone}", Country: "${testCase.country}"`);
        console.log('-'.repeat(40));
        
        try {
            const result = await validatePhoneNumber(testCase.phone, testCase.country);
            
            if (result.valid) {
                console.log('✅ VALID');
                console.log(`   Formatted: ${result.phoneNumber}`);
                console.log(`   Country: ${result.countryCode}`);
                passedTests++;
            } else {
                console.log('❌ INVALID');
                if (result.error) {
                    console.log(`   Error: ${result.error}`);
                }
            }
        } catch (error) {
            console.log('💥 TEST ERROR');
            console.log(`   Error: ${error.message}`);
        }
        
        // Add a small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('\n' + '='.repeat(60));
    console.log(`📊 Test Results: ${passedTests}/${totalTests} tests completed`);
    console.log('='.repeat(60));
}

// Interactive test function
async function testSingleNumber() {
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    return new Promise((resolve) => {
        rl.question('Enter phone number to test: ', (phoneNumber) => {
            rl.question('Enter country code (e.g., US, GB, IN): ', async (countryCode) => {
                console.log('\n🔍 Testing your number...\n');
                
                try {
                    const result = await validatePhoneNumber(phoneNumber, countryCode);
                    
                    if (result.valid) {
                        console.log('✅ Phone number is VALID!');
                        console.log(`   Original: ${phoneNumber}`);
                        console.log(`   Formatted: ${result.phoneNumber}`);
                        console.log(`   Country: ${result.countryCode}`);
                    } else {
                        console.log('❌ Phone number is INVALID');
                        if (result.error) {
                            console.log(`   Error: ${result.error}`);
                        }
                    }
                } catch (error) {
                    console.log('💥 Error occurred:', error.message);
                }
                
                rl.close();
                resolve();
            });
        });
    });
}

// Main execution
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--interactive') || args.includes('-i')) {
        console.log('🎯 Interactive Phone Number Validator\n');
        await testSingleNumber();
    } else if (args.includes('--help') || args.includes('-h')) {
        console.log('📞 Phone Number Validator Test Suite\n');
        console.log('Usage:');
        console.log('  node testPhoneValidator.js           # Run all test cases');
        console.log('  node testPhoneValidator.js -i        # Interactive mode');
        console.log('  node testPhoneValidator.js --help    # Show this help');
        console.log('\nMake sure to set your Twilio credentials:');
        console.log('  export TWILIO_ACCOUNT_SID="your_account_sid"');
        console.log('  export TWILIO_AUTH_TOKEN="your_auth_token"');
    } else {
        await runTests();
    }
}

// Check if Twilio credentials are set
if (process.env.TWILIO_ACCOUNT_SID === undefined || process.env.TWILIO_AUTH_TOKEN === undefined) {
    console.log('⚠️  Warning: Twilio credentials not found in environment variables');
    console.log('Please set TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN before running tests');
    console.log('\nExample:');
    console.log('export TWILIO_ACCOUNT_SID="your_account_sid"');
    console.log('export TWILIO_AUTH_TOKEN="your_auth_token"');
    console.log('\nThen run: node testPhoneValidator.js\n');
}

main().catch(console.error);
