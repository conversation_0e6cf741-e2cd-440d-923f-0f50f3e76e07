# Phone Number Validator Test Setup

## Prerequisites
1. Node.js installed on your system
2. A Twilio account with Account S<PERSON> and Auth Token

## Setup Instructions

### 1. Install Dependencies
```bash
npm install
```

### 2. Set Twilio Credentials
Replace `your_account_sid` and `your_auth_token` with your actual Twilio credentials:

**Option A: Environment Variables (Recommended)**
```bash
export TWILIO_ACCOUNT_SID="your_actual_account_sid"
export TWILIO_AUTH_TOKEN="your_actual_auth_token"
```

**Option B: Edit phoneValidator.js directly**
Update the credentials in the phoneValidator.js file.

### 3. Run Tests

**Run all test cases:**
```bash
npm test
# or
node testPhoneValidator.js
```

**Interactive mode (test individual numbers):**
```bash
npm run test:interactive
# or
node testPhoneValidator.js --interactive
```

**Show help:**
```bash
node testPhoneValidator.js --help
```

## Test Cases Included

The test suite includes:
- ✅ Valid US numbers (with/without country code, formatted)
- ✅ Valid international numbers (UK, France, India)
- ❌ Invalid numbers (too short, too long, non-numeric)
- 🔍 Edge cases (all zeros, all ones, empty strings)

## Expected Output

Valid numbers will show:
- ✅ VALID status
- Formatted phone number
- Country code

Invalid numbers will show:
- ❌ INVALID status
- Error message (if applicable)

## Notes

- The test includes a 500ms delay between requests to avoid Twilio rate limiting
- Make sure your Twilio account has sufficient credits for Lookup API calls
- Each validation request may incur a small charge from Twilio
