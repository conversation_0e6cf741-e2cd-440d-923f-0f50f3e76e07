const twilio = require('twilio');

const accountSid = process.env.TWILIO_ACCOUNT_SID || 'your_account_sid'; // Replace with your Twilio Account SID
const authToken = process.env.TWILIO_AUTH_TOKEN || 'your_auth_token';   // Replace with your Twilio Auth Token
const client = new twilio(accountSid, authToken);

async function validatePhoneNumber(phoneNumber, countryCode) {
    try {
        const response = await client.lookups.v2
            .phoneNumbers(phoneNumber)
            .fetch({ 
                fields: 'valid,format',
                countryCode: countryCode // Specify the region (e.g., 'US', 'GB', 'IN')
            });
        
        if (response.valid) {
            console.log('Phone number is valid!');
            console.log('Formatted number:', response.phoneNumber);
            console.log('Country code:', response.countryCode);
            return {
                valid: true,
                phoneNumber: response.phoneNumber,
                countryCode: response.countryCode
            };
        } else {
            console.log('Phone number is invalid.');
            return {
                valid: false,
                phoneNumber: null,
                countryCode: null
            };
        }
    } catch (error) {
        console.error('Error validating phone number:', error.message);
        return {
            valid: false,
            error: error.message,
            phoneNumber: null,
            countryCode: null
        };
    }
}

module.exports = { validatePhoneNumber };
